<!-- Enhanced Filtering Section with Alpine.js -->
<div x-data="filterPanel()" class="mb-6 bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
  <!-- Filter Header -->
  <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-800 flex items-center">
        <svg class="w-5 h-5 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
        </svg>
        Filter Transactions
      </h3>
      <div class="flex items-center space-x-2">
        <!-- Clear All Filters Button -->
        <button 
          @click="clearAllFilters()"
          class="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100 transition-colors"
        >
          Clear All
        </button>
        <!-- Collapse/Expand Button -->
        <button 
          @click="toggle()"
          class="p-1 rounded-md hover:bg-gray-100 transition-colors"
          :aria-expanded="isExpanded"
        >
          <svg 
            class="w-5 h-5 text-gray-600 transition-transform duration-200" 
            :class="{ 'rotate-180': !isExpanded }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Filter Content -->
  <div 
    x-show="isExpanded"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 max-h-0"
    x-transition:enter-end="opacity-100 max-h-96"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100 max-h-96"
    x-transition:leave-end="opacity-0 max-h-0"
    class="overflow-hidden"
  >
    <div class="p-6">
      <!-- Search Box with Alpine.js -->
      <div x-data="searchBox()" class="mb-6">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
          Search Transactions
        </label>
        <div class="relative">
          <input
            type="text"
            x-model="query"
            placeholder="Search by description, reference, or amount..."
            class="w-full border border-gray-300 rounded-md px-4 py-2 pr-10 text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <!-- Search Icon -->
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <svg 
              x-show="!isSearching" 
              class="h-5 w-5 text-gray-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <!-- Loading Spinner -->
            <svg 
              x-show="isSearching" 
              class="animate-spin h-5 w-5 text-blue-500" 
              fill="none" 
              viewBox="0 0 24 24"
            >
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <!-- Clear Search Button -->
          <button 
            x-show="query.length > 0"
            @click="clear()"
            class="absolute inset-y-0 right-8 pr-3 flex items-center"
          >
            <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Filter Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Date Filter -->
        <div class="space-y-2">
          <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
          <form phx-change="filter_by_date">
            <select name="date" id="date" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Dates</option>
              <%= for date <- @unique_dates do %>
                <option value={date} selected={@selected_date == date}>
                  <%= format_filter_date(date) %>
                </option>
              <% end %>
            </select>
          </form>
        </div>

        <!-- Status Filter -->
        <div class="space-y-2">
          <label for="status" class="block text-sm font-medium text-gray-700">Match Status</label>
          <form phx-change="filter_by_status">
            <select name="status" id="status" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Status</option>
              <option value="matched" selected={@selected_status == "matched"}>✅ Matched</option>
              <option value="unmatched" selected={@selected_status == "unmatched"}>❌ Unmatched</option>
            </select>
          </form>
        </div>

        <!-- Type Filter -->
        <div class="space-y-2">
          <label for="type" class="block text-sm font-medium text-gray-700">Transaction Type</label>
          <form phx-change="filter_by_type">
            <select name="type" id="type" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Types</option>
              <option value="debit" selected={@selected_type == "debit"}>💸 Debit</option>
              <option value="credit" selected={@selected_type == "credit"}>💰 Credit</option>
            </select>
          </form>
        </div>

        <!-- Run Filter -->
        <div class="space-y-2">
          <label for="run_id" class="block text-sm font-medium text-gray-700">Reconciliation Run</label>
          <form phx-change="filter_by_run">
            <select name="run_id" id="run_id" class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Runs</option>
              <%= for run <- @reconciliation_runs do %>
                <%
                  # Determine status indicator and styling
                  {status_icon, status_text, option_class} = case run.status do
                    "pending" -> {"🟡", "PENDING", "background-color: #fef3c7; font-weight: bold;"}
                    "completed" -> {"✅", "COMPLETED", ""}
                    "processing" -> {"🔄", "PROCESSING", "background-color: #dbeafe;"}
                    _ -> {"❓", "UNKNOWN", "background-color: #f3f4f6;"}
                  end
                %>
                <option 
                  value={run.id} 
                  selected={@selected_run_id == to_string(run.id)}
                  style={option_class}
                >
                  <%= status_icon %> <%= run.name %> - <%= status_text %>
                </option>
              <% end %>
            </select>
          </form>
        </div>
      </div>

      <!-- Active Filters Display -->
      <%= if @selected_date || @selected_status || @selected_type || @selected_run_id || (@search_query && @search_query != "") do %>
        <div class="mt-6 pt-4 border-t border-gray-200">
          <div class="flex flex-wrap items-center gap-2">
            <span class="text-sm font-medium text-gray-700">Active filters:</span>
            <%= if @selected_date do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Date: <%= format_filter_date(@selected_date) %>
              </span>
            <% end %>
            <%= if @selected_status do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Status: <%= String.capitalize(@selected_status) %>
              </span>
            <% end %>
            <%= if @selected_type do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Type: <%= String.capitalize(@selected_type) %>
              </span>
            <% end %>
            <%= if @selected_run_id do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                Run: <%= get_run_name(@reconciliation_runs, @selected_run_id) %>
              </span>
            <% end %>
            <%= if @search_query && @search_query != "" do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                Search: "<%= @search_query %>"
              </span>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Enhanced Export Button with Alpine.js -->
<div x-data="exportButton()" class="mb-6">
  <button 
    @click="startExport()"
    :disabled="isExporting"
    :class="buttonClass"
    phx-click="export_transactions"
    phx-value-run_id={@selected_run_id}
  >
    <span x-show="!isExporting" class="flex items-center">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <span x-text="buttonText"></span>
    </span>
    <span x-show="isExporting" class="flex items-center">
      <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span x-text="buttonText"></span>
    </span>
  </button>
</div>
