<header class="w-5/6 mx-auto text-gray-800 p-4 mt-8 flex justify-between items-center shadow rounded-lg border-t border-orange-500" style="background: rgba(249, 250, 251, 0.95); backdrop-filter: blur(20px);">

  <!-- System Status -->
  <div class="flex items-center justify-between">
    <div class="flex items-center mr-6">
      <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      <span class="ml-2 text-sm font-medium text-gray-800">System Online</span>
    </div>
    <div class="text-sm text-gray-600 mr-6">
      <%= Date.utc_today() |> Calendar.strftime("%b %d") %>
    </div>
  </div>

  <!-- User Info -->
  <div class="flex items-center">
    <%= if @current_user do %>
      <!-- User Dropdown with Alpine.js -->
      <div class="relative" x-data="{ open: false }" @click.away="open = false">
        <!-- Dropdown Trigger -->
        <button
          type="button"
          class="flex items-center text-gray-800 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 rounded-lg px-3 py-2 transition-colors duration-200"
          @click="open = !open"
          :aria-expanded="open"
          aria-haspopup="true"
        >
          <span class="text-sm font-medium"><%= @current_user.email %></span>
          <svg class="ml-2 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>

        <!-- Dropdown Menu -->
        <div
          x-show="open"
          x-transition:enter="transition ease-out duration-200"
          x-transition:enter-start="opacity-0 scale-95"
          x-transition:enter-end="opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-150"
          x-transition:leave-start="opacity-100 scale-100"
          x-transition:leave-end="opacity-0 scale-95"
          class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
          style="background: rgba(249, 250, 251, 0.98); backdrop-filter: blur(20px);"
        >
          <!-- Change Password -->
          <a
            href={~p"/users/settings"}
            class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-150"
          >
            
            Change Password
          </a>

          <!-- Divider -->
          <div class="border-t border-gray-200 my-1"></div>

          <!-- Logout -->
          <.form action={~p"/users/log_out"} method="delete" class="block">
            <button
              type="submit"
              class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 transition-colors duration-150 text-left"
            >
              <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              Logout
            </button>
          </.form>
        </div>
      </div>
    <% end %>
  </div>
</header>