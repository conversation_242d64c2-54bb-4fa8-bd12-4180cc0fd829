// Alpine.js Components for Reconciliation App

// Filter Panel Component
document.addEventListener('alpine:init', () => {
  Alpine.data('filterPanel', () => ({
    isExpanded: true,
    
    toggle() {
      this.isExpanded = !this.isExpanded
    },
    
    clearAllFilters() {
      // Trigger Phoenix events to clear filters
      this.$dispatch('clear-filters')
    }
  }))

  // Export Button Component
  Alpine.data('exportButton', () => ({
    isExporting: false,
    exportState: 'idle', // idle, processing, complete, error
    
    init() {
      // Watch for export state changes from LiveView
      this.$watch('exportState', (value) => {
        this.isExporting = value === 'processing'
        
        if (value === 'complete') {
          setTimeout(() => {
            this.exportState = 'idle'
          }, 2000)
        }
      })
    },
    
    startExport() {
      this.isExporting = true
      this.exportState = 'processing'
    },
    
    get buttonText() {
      switch(this.exportState) {
        case 'processing': return 'Exporting...'
        case 'complete': return 'Export Complete!'
        case 'error': return 'Export Failed'
        default: return 'Export to Excel'
      }
    },
    
    get buttonClass() {
      const base = 'px-4 py-2 rounded-lg font-medium transition-all duration-200 '
      switch(this.exportState) {
        case 'processing': return base + 'bg-yellow-500 text-white cursor-not-allowed'
        case 'complete': return base + 'bg-green-500 text-white'
        case 'error': return base + 'bg-red-500 text-white'
        default: return base + 'bg-orange-500 hover:bg-orange-600 text-white'
      }
    }
  }))

  // Transaction Row Component
  Alpine.data('transactionRow', (transaction) => ({
    isExpanded: false,
    transaction: transaction,
    
    toggle() {
      this.isExpanded = !this.isExpanded
    },
    
    get statusColor() {
      return this.transaction.is_matched ? 'text-green-400' : 'text-red-400'
    },
    
    get statusIcon() {
      return this.transaction.is_matched ? '✓' : '✗'
    }
  }))

  // Search Component with debouncing
  Alpine.data('searchBox', () => ({
    query: '',
    isSearching: false,
    searchTimeout: null,
    
    init() {
      this.$watch('query', (value) => {
        this.isSearching = true
        
        // Clear existing timeout
        if (this.searchTimeout) {
          clearTimeout(this.searchTimeout)
        }
        
        // Debounce search for 300ms
        this.searchTimeout = setTimeout(() => {
          this.$dispatch('search-transactions', { query: value })
          this.isSearching = false
        }, 300)
      })
    },
    
    clear() {
      this.query = ''
    }
  }))

  // Toast Notification Component (enhanced version)
  Alpine.data('toastManager', () => ({
    toasts: [],
    
    show(type, title, message, duration = 4000) {
      const id = Date.now()
      const toast = { id, type, title, message, visible: false }
      
      this.toasts.push(toast)
      
      // Animate in
      setTimeout(() => {
        toast.visible = true
      }, 10)
      
      // Auto remove
      setTimeout(() => {
        this.remove(id)
      }, duration)
    },
    
    remove(id) {
      const index = this.toasts.findIndex(t => t.id === id)
      if (index > -1) {
        this.toasts[index].visible = false
        setTimeout(() => {
          this.toasts.splice(index, 1)
        }, 300)
      }
    },
    
    getToastClass(type) {
      const base = 'fixed top-4 right-4 z-50 max-w-sm w-full bg-white border rounded-lg shadow-lg p-4 transform transition-all duration-300 '
      const colors = {
        success: 'border-green-500 bg-green-50',
        error: 'border-red-500 bg-red-50',
        warning: 'border-yellow-500 bg-yellow-50',
        info: 'border-blue-500 bg-blue-50'
      }
      return base + (colors[type] || colors.info)
    }
  }))

  // Pagination Component
  Alpine.data('pagination', () => ({
    currentPage: 1,
    totalPages: 1,
    
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
        this.$dispatch('change-page', { page })
      }
    },
    
    nextPage() {
      this.goToPage(this.currentPage + 1)
    },
    
    prevPage() {
      this.goToPage(this.currentPage - 1)
    },
    
    get visiblePages() {
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      const pages = []
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }
  }))

  // Modal Component
  Alpine.data('modal', () => ({
    isOpen: false,
    
    open() {
      this.isOpen = true
      document.body.style.overflow = 'hidden'
    },
    
    close() {
      this.isOpen = false
      document.body.style.overflow = 'auto'
    },
    
    closeOnEscape(event) {
      if (event.key === 'Escape') {
        this.close()
      }
    }
  }))

  // Currency Formatter
  Alpine.data('currencyFormatter', () => ({
    format(amount, currency = 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(amount)
    }
  }))
})

// Global Alpine.js utilities
window.Alpine = Alpine

// Custom Alpine.js directives
Alpine.directive('currency', (el, { expression }, { evaluate }) => {
  const amount = evaluate(expression)
  el.textContent = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
})

Alpine.directive('tooltip', (el, { expression }, { evaluate }) => {
  const text = evaluate(expression)
  el.title = text
  el.classList.add('cursor-help')
})
